<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS BASICS</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" type="image/x-icon" href="/A01_CSS_BASICS/favicon.ico">
</head>
<body>
    
    <header class="header">
        <h1>CSS BASICS</h1>
        <p class="header-paragraph">This is a <span class="nested">simple</span> <span class="html">HTML</span> page with CSS styling.</p>
    </header>

    <hr>

    <article class="highlight">
        <h2>Article Title</h2>
        <p id="first-p">This is the content of the article. It can include text, images, and other elements.</p>
        <p id="second-p">This is the content of the article. It can include text, images, and other elements.</p>

        <button id="button">Click Me</button>
    </article>

    <hr>

    <article class="highlight">
        <h2 class="remove-highlight">Article Title</h2>
        <p>This is the content of the article. It can include text, images, and other elements.</p>
        <p>This is the content of the article. It can include text, images, and other elements.</p>

        <button id="button">Click Me</button>
    </article>

    <hr>

    <article class="better-first">
        <h2>Article Title</h2>
        <p>This is the content of the article. It can include text, images, and other elements.</p>
        <p>This is the content of the article. It can include text, images, and other elements.</p>

        <button id="button">Click Me</button>
    </article>

    <article class="better-second">
        <h2>Article Title</h2>
        <p>This is the content of the article. It can include text, images, and other elements.</p>
        <p>This is the content of the article. It can include text, images, and other elements.</p>

        <button>Click Me</button>
    </article>

    

    <hr>

</body>
</html>