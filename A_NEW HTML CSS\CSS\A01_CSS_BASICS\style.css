/* 
==> CSS Types

1. Inline CSS : writin in html file and in tag
2. Internal CSS : writing in html file and in head tag
3. External CSS : writing in external file and linking in html file
 
==>Popular CSS Selectors

1. Element: p { … } (all <p>).

2. Class: .box { … }.

3. ID: #main { … }.

4. Universal: * { … } (all elements).

5. Descendant: div p { … } (all <p> inside <div>).

6. Group: h1, h2, h3 { … }. 


==> CSS Style Priority

element < class < id < inline < !important


Class Selector
.box { … }
👉 Stronger than element/universal. Good for reusable styles.

Attribute Selector
input[type="text"] { … }
👉 Same strength as class.

Pseudo-classes
a:hover { … }
👉 Same strength as class.

ID Selector
#main { … }
👉 Very strong. Meant for unique elements.

Inline Styles
<p style="color: red;">
👉 Super strong. Beats all selectors above.

!important
p { color: blue !important; }
👉 Overrides everything (but use carefully—it’s like breaking the rules).


*/

*{
    padding: 0;
}


.highlight {
    color: black;
    background-color: yellow;
    font-weight: bold;
}

.remove-highlight {
    background-color: white;
}

/* Grouping classes */
.better-first, .better-second{
    color: black;
    background-color: lightblue;
    font-weight: bold;
}


/* Grouping ids */
#first-p, #second-p{
    color: red;
}

#button{
    color: white;
    background-color: green;
    border-radius: 5px;
    cursor: pointer;
}

/* Nested selectors */

.header>.header-paragraph>.nested{
    color: blue;
    font-weight: bold;
}

/* pseudo selectors */
h1:hover, p:hover {  
    background-color: blue;
}

/* !important  : !important is used to override all the other styles */

.html {
    color: orange !important;
}

.html {
    color: red ;
}